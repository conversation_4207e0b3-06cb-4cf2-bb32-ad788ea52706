import pandas as pd
import json
import re
import os
import pickle
from typing import List, Dict
from contextlib import contextmanager
import sys
import torch
from tqdm import tqdm
import numpy as np
import faiss
# Thêm import cho chuẩn hóa văn bản và word segmentation
from underthesea import text_normalize, word_tokenize
from transformers import pipeline

def load_data(train_path='train.json', legal_corpus_path='legal_corpus.json'):
    # Đọc file train.json
    with open(train_path, 'r', encoding='utf-8') as f:
        train_data = json.load(f)
    train_df = pd.DataFrame(train_data)

    # Đọc file legal_corpus.json
    with open(legal_corpus_path, 'r', encoding='utf-8') as f:
        legal_corpus = json.load(f)

    # Chuyển đổi legal_corpus thành DataFrame
    legal_articles = []
    for doc in legal_corpus:
        for article in doc['content']:
            legal_articles.append({
                'doc_id': doc['id'],
                'law_id': doc['law_id'],
                'aid': article['aid'],
                'content_Article': article['content_Article']
            })

    legal_df = pd.DataFrame(legal_articles)

    return train_df, legal_df

train_df, legal_df = load_data('train.json', 'legal_corpus.json')

# Examine the data
print(f"Train data shape: {train_df.shape}")
print(f"Legal corpus shape: {legal_df.shape}")

